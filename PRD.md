
# P2P System for Srd.Exchange

## Overview

This document outlines the requirements for developing a P2P platform for Srd.Exchange. The system will allow users to buy and sell USDT with INR, with all transactions processed via the admin. The platform will facilitate smooth interactions between buyers and sellers, ensuring safety, manual verification, and smooth operation.

---

## Key Features

### 1. **User Flow**

* **Landing Page**:

  * Hero section with "Connect Wallet" button.
  * Buy and Sell togglers should be visible.
* **User Login**:

  * Users must connect their wallet (Metamask or Trust Wallet).
  * On connection, system checks Supabase for existing user data (bank/UPI).
  * If missing, user is prompted to submit details (stored in Supabase).

### 2. **Sell USDT Flow**

* **User Action**:

  * Seller enters amount to sell (min. 11 USDT).
  * On confirmation, USDT is transferred to Admin wallet.
  * Entry is stored in Supabase: seller wallet, amount, timestamp, etc.
* **Admin Processing**:

  * Admin sets sell rate (e.g., 88 INR/USDT), calculates platform cut.
  * Seller’s UPI/bank info fetched from Supabase is shown to buyer.

### 3. **Buy USDT Flow**

* **Buyer Action**:

  * Buyer sees live sell listings from Supabase.
  * Price (e.g., 92 INR/USDT) is shown.
  * Buyer makes payment to seller’s UPI/bank.
  * Uploads payment proof (image stored in Supabase Storage, ref in DB).

### 4. **Manual Verification**

* **Admin Role**:

  * Admin gets alert from Supabase on new proof uploads.
  * Verifies payment manually, confirms with seller.
  * On confirmation, clicks "Release"; USDT sent to buyer.
  * Status updated in Supabase.

---

## Admin Panel Features

1. **User Management**:

   * View/edit users from Supabase (wallets, bank/UPI info).
2. **Transaction Management**:

   * Manage buy/sell orders (fetched from Supabase).
   * View confirmation statuses and proofs.
3. **Manual Verification**:

   * Verify proofs and release USDT.
   * Update transaction status in Supabase.

---

## User Interface

### 1. **User Landing Page**

* Connect Wallet button.
* "Buy" / "Sell" toggle.
* UPI/bank form (auto-check via Supabase).
* Listings from Supabase (live USDT offers).

### 2. **Admin Page**

* Dashboard showing all users and orders.
* Access to proof uploads via Supabase Storage.
* Buttons to verify and release USDT.

---

## Backend Requirements

1. **Wallet Integration**:

   * Metamask/Trust Wallet for blockchain txns.
2. **Supabase Integration**:

   * **Tables**:

     * `users`: wallet, name, UPI/bank, created\_at.
     * `orders`: type (buy/sell), amount, rate, seller/buyer wallet, status.
     * `proofs`: image URL, buyer wallet, txn\_id, uploaded\_at.
   * **Storage**: Proof images.
   * **Auth**: Wallet-based pseudo-auth using Supabase row-level policies.
3. **Payment Handling**:

   * INR payments via UPI/bank, proof captured and stored.

---

## Notifications

1. **Admin**:

   * Realtime updates from Supabase (on `proofs` insert).
2. **Seller**:

   * Notified when buyer uploads proof (optional via Supabase triggers + email/SMS/webhook).

---

## Color Scheme

* **Background**: #0D0D0D
* **Primary Accent**: #E63946
* **Secondary Accent**: #FFC300
* **Text**: #FDFDFD
* **Buttons**: #F4A261
* **Hover**: #FF5733

---

## Conclusion

With Supabase integration, this P2P system enables secure storage, real-time sync, and easy management of user data, transactions, and verification workflows. The backend remains simple, scalable, and developer-friendly.
